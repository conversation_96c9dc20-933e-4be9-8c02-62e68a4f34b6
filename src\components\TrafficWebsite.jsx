import { Box, Typography, LinearProgress } from "@mui/material";

function TrafficWebsite() {
  const trafficData = [
    { name: "Google", value: 85, color: "#4285f4" },
    { name: "YouTube", value: 70, color: "#ff0000" },
    { name: "Instagram", value: 45, color: "#e4405f" },
    { name: "Pinterest", value: 60, color: "#bd081c" },
    { name: "Facebook", value: 35, color: "#1877f2" },
    { name: "Twitter", value: 50, color: "#1da1f2" },
  ];

  return (
    <Box
      sx={{
        bgcolor: "#f9f9f9",
        p: 4,
        borderRadius: 5,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#333",
        }}
      >
        Traffic by Website
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 2.5 }}>
        {trafficData.map((item, index) => (
          <Box key={index}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                mb: 1,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  fontWeight: 500,
                  color: "#333",
                  fontSize: "14px",
                }}
              >
                {item.name}
              </Typography>
            </Box>

            <Box sx={{ position: "relative", width: "100%" }}>
              <LinearProgress
                variant="determinate"
                value={item.value}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  bgcolor: "#e0e0e0",
                  "& .MuiLinearProgress-bar": {
                    bgcolor: "#333",
                    borderRadius: 3,
                  },
                }}
              />
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
}

export default TrafficWebsite;
