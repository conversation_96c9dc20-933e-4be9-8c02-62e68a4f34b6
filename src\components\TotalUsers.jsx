import { useState } from "react";
import { <PERSON><PERSON><PERSON>, Line, XAxi<PERSON>, YAxis, ResponsiveContainer } from "recharts";
import { Box, Typography, Tabs, Tab } from "@mui/material";

function TotalUsers() {
  const [activeTab, setActiveTab] = useState(0);

  const totalUsersData = [
    { month: "Jan", thisYear: 12000, lastYear: 8000 },
    { month: "Feb", thisYear: 8000, lastYear: 12000 },
    { month: "Mar", thisYear: 15000, lastYear: 20000 },
    { month: "Apr", thisYear: 22000, lastYear: 15000 },
    { month: "May", thisYear: 28000, lastYear: 8000 },
    { month: "Jun", thisYear: 20000, lastYear: 25000 },
    { month: "Jul", thisYear: 25000, lastYear: 30000 },
  ];

  const tabData = [
    { label: "Total Users", data: totalUsersData, yAxisDomain: [0, 30000] },
    { label: "Total Projects" },
    {
      label: "Operating Status",
    },
  ];

  const currentData = tabData[activeTab];

  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 5,
        p: 2,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
      }}
    >
      <Box
        sx={{
          display: "flex",
          mb: 4,
        }}
      >
        <Tabs
          value={activeTab}
          sx={{
            "& .MuiTab-root": {
              textTransform: "none",
              fontSize: "14px",
              fontWeight: 500,
              color: "#6B7280",
              minWidth: "auto",
              padding: "8px 16px",
              "&.Mui-selected": {
                color: "#111827",
                fontWeight: 600,
              },
            },
            "& .MuiTabs-indicator": {
              backgroundColor: "#111827",
              height: 2,
            },
          }}
        >
          {tabData.map((tab, index) => (
            <Tab key={index} label={tab.label} />
          ))}
        </Tabs>
        <Box
          sx={{
            display: "flex",
            gap: 3,
            justifyContent: "flex-end",
            pl: 2,
            borderLeft: 1,
            borderColor: "divider",
            borderWidth: "3px",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: "50%",
                backgroundColor: "#111827",
              }}
            />
            <Typography
              variant="body2"
              sx={{ color: "#6B7280", fontSize: "12px" }}
            >
              This year
            </Typography>
          </Box>
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: "50%",
                backgroundColor: "#93C5FD",
              }}
            />
            <Typography
              variant="body2"
              sx={{ color: "#6B7280", fontSize: "12px" }}
            >
              Last year
            </Typography>
          </Box>
        </Box>
      </Box>

      <Box
        sx={{
          height: "312px",
          width: "100%",
          "& *": {
            outline: "none ",
          },
        }}
      >
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={currentData.data}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <XAxis
              dataKey="month"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#9CA3AF" }}
              style={{ outline: "none" }}
            />
            <YAxis
              domain={currentData.yAxisDomain}
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: "#9CA3AF" }}
              tickFormatter={(value) => {
                if (activeTab === 0) {
                  return `${value / 1000}K`;
                }
                return value;
              }}
              style={{ outline: "none" }}
            />
            <Line
              type="monotone"
              dataKey="thisYear"
              stroke="#111827"
              strokeWidth={2}
              dot={false}
              fill="#F3F4F6"
              fillOpacity={0.3}
            />
            <Line
              type="monotone"
              dataKey="lastYear"
              stroke="#93C5FD"
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
}

export default TotalUsers;
