import { Box, Typography } from "@mui/material";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ResponsiveC<PERSON>r,
  Cell,
} from "recharts";

const data = [
  {
    name: "Linux",
    value: 18000,
  },
  {
    name: "<PERSON>",
    value: 30000,
  },
  {
    name: "iOS",
    value: 22000,
  },
  {
    name: "Windows",
    value: 35000,
  },
  {
    name: "Android",
    value: 14000,
  },
  {
    name: "Other",
    value: 26000,
  },
];

const colors = [
  "#8B5CF6",
  "#10B981",
  "#000000",
  "#3B82F6",
  "#93C5FD",
  "#34D399",
];

function TrafficDevice() {
  return (
    <Box
      sx={{
        backgroundColor: "#f9f9f9",
        borderRadius: 5,
        p: 3,
        boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
        height: 400,
      }}
    >
      <Typography
        variant="h6"
        sx={{
          fontWeight: "bold",
          mb: 3,
          color: "#1e1e1e",
        }}
      >
        Traffic by Website
      </Typography>

      <ResponsiveContainer width="100%" height={300}>
        <BarChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 5,
          }}
          barCategoryGap="20%"
        >
          <XAxis
            dataKey="name"
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: "#9CA3AF" }}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fontSize: 12, fill: "#9CA3AF" }}
            tickFormatter={(value) => `${value / 1000}K`}
          />
          <Bar dataKey="value" radius={[8, 8, 8, 8]}>
            {data.map((_, index) => (
              <Cell key={`cell-${index}`} fill={colors[index]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </Box>
  );
}

export default TrafficDevice;
